@model IEnumerable<InventoryManagementSystem.Models.SaleInvoice>

@{
    ViewData["Title"] = "فواتير البيع";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-cart-check"></i> فواتير البيع</h1>
    <a asp-action="Create" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> فاتورة بيع جديدة
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>تاريخ الفاتورة</th>
                            <th>عدد الأصناف</th>
                            <th>إجمالي الكمية</th>
                            <th>الملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    <strong>@Html.DisplayFor(modelItem => item.InvoiceNumber)</strong>
                                </td>
                                <td>@Html.DisplayFor(modelItem => item.Customer.Name)</td>
                                <td>@item.InvoiceDate.ToString("yyyy/MM/dd")</td>
                                <td>
                                    <span class="badge bg-info">@item.Items.Count</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">@item.Items.Sum(i => i.Quantity)</span>
                                </td>
                                <td>@Html.DisplayFor(modelItem => item.Notes)</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-outline-info btn-sm">
                                            <i class="bi bi-eye"></i> عرض
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i> حذف
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-cart-check fs-1 text-muted"></i>
                <h4 class="text-muted mt-3">لا توجد فواتير بيع</h4>
                <p class="text-muted">ابدأ بإضافة فاتورة بيع جديدة</p>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> فاتورة بيع جديدة
                </a>
            </div>
        }
    </div>
</div>
