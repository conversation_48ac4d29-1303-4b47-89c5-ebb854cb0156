@model InventoryManagementSystem.Models.Customer

@{
    ViewData["Title"] = "إضافة عميل جديد";
}

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-plus-circle"></i> إضافة عميل جديد</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="mb-3">
                        <label asp-for="Name" class="form-label">اسم العميل</label>
                        <input asp-for="Name" class="form-control" placeholder="أدخل اسم العميل" />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Phone" class="form-label">رقم الهاتف</label>
                                <input asp-for="Phone" class="form-control" placeholder="أدخل رقم الهاتف" />
                                <span asp-validation-for="Phone" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Email" class="form-label">البريد الإلكتروني</label>
                                <input asp-for="Email" class="form-control" type="email" placeholder="أدخل البريد الإلكتروني" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Address" class="form-label">العنوان</label>
                        <textarea asp-for="Address" class="form-control" rows="3" placeholder="أدخل عنوان العميل"></textarea>
                        <span asp-validation-for="Address" class="text-danger"></span>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> حفظ
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
