@model InventoryManagementSystem.Models.Category

@{
    ViewData["Title"] = "إضافة فئة جديدة";
}

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-plus-circle"></i> إضافة فئة جديدة</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="mb-3">
                        <label asp-for="Name" class="form-label">اسم الفئة</label>
                        <input asp-for="Name" class="form-control" placeholder="أدخل اسم الفئة" />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Description" class="form-label">الوصف</label>
                        <textarea asp-for="Description" class="form-control" rows="3" placeholder="أدخل وصف الفئة (اختياري)"></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> حفظ
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
