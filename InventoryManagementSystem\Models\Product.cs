using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagementSystem.Models
{
    public class Product
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? Code { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        [StringLength(20)]
        public string Unit { get; set; } = "قطعة"; // الوحدة (قطعة، كيلو، متر، إلخ)
        
        public int CategoryId { get; set; }
        
        public int MinimumStock { get; set; } = 0; // الحد الأدنى للمخزون
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // Navigation Properties
        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; } = null!;
        
        public virtual ICollection<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; } = new List<PurchaseInvoiceItem>();
        public virtual ICollection<SaleInvoiceItem> SaleInvoiceItems { get; set; } = new List<SaleInvoiceItem>();
        public virtual ICollection<BOMItem> BOMItems { get; set; } = new List<BOMItem>();
        public virtual ICollection<BOM> BOMs { get; set; } = new List<BOM>();
        public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
        public virtual Inventory? Inventory { get; set; }
    }
}
