﻿@{
    ViewData["Title"] = "لوحة التحكم";
    var dashboardData = ViewBag.DashboardData;
}

<div class="row">
    <div class="col-12">
        <h1 class="mb-4"><i class="bi bi-speedometer2"></i> لوحة التحكم</h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>@(dashboardData?.TotalProducts ?? 0)</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-box fs-1"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="@Url.Action("Index", "Products")" class="text-white text-decoration-none">
                    عرض التفاصيل <i class="bi bi-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>@(dashboardData?.TotalSuppliers ?? 0)</h4>
                        <p class="mb-0">الموردين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-truck fs-1"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="@Url.Action("Index", "Suppliers")" class="text-white text-decoration-none">
                    عرض التفاصيل <i class="bi bi-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>@(dashboardData?.TotalCustomers ?? 0)</h4>
                        <p class="mb-0">العملاء</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="@Url.Action("Index", "Customers")" class="text-white text-decoration-none">
                    عرض التفاصيل <i class="bi bi-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>@(dashboardData?.LowStockProducts ?? 0)</h4>
                        <p class="mb-0">منتجات قليلة المخزون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle fs-1"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="@Url.Action("Index", "Products")" class="text-dark text-decoration-none">
                    عرض التفاصيل <i class="bi bi-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-cart-plus"></i> آخر فواتير الشراء</h5>
            </div>
            <div class="card-body">
                @if (dashboardData?.RecentPurchases != null && ((IEnumerable<dynamic>)dashboardData.RecentPurchases).Any())
                {
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var purchase in dashboardData.RecentPurchases)
                                {
                                    <tr>
                                        <td>@purchase.InvoiceNumber</td>
                                        <td>@purchase.Supplier.Name</td>
                                        <td>@purchase.InvoiceDate.ToString("yyyy/MM/dd")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <p class="text-muted">لا توجد فواتير شراء حديثة</p>
                }
            </div>
            <div class="card-footer">
                <a href="@Url.Action("Index", "PurchaseInvoices")" class="btn btn-primary btn-sm">
                    عرض جميع فواتير الشراء
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-cart-check"></i> آخر فواتير البيع</h5>
            </div>
            <div class="card-body">
                @if (dashboardData?.RecentSales != null && ((IEnumerable<dynamic>)dashboardData.RecentSales).Any())
                {
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var sale in dashboardData.RecentSales)
                                {
                                    <tr>
                                        <td>@sale.InvoiceNumber</td>
                                        <td>@sale.Customer.Name</td>
                                        <td>@sale.InvoiceDate.ToString("yyyy/MM/dd")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <p class="text-muted">لا توجد فواتير بيع حديثة</p>
                }
            </div>
            <div class="card-footer">
                <a href="@Url.Action("Index", "SaleInvoices")" class="btn btn-success btn-sm">
                    عرض جميع فواتير البيع
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-lightning"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="@Url.Action("Create", "Products")" class="btn btn-outline-primary w-100">
                            <i class="bi bi-plus-circle"></i> إضافة منتج جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="@Url.Action("Create", "PurchaseInvoices")" class="btn btn-outline-success w-100">
                            <i class="bi bi-cart-plus"></i> فاتورة شراء جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="@Url.Action("Create", "SaleInvoices")" class="btn btn-outline-info w-100">
                            <i class="bi bi-cart-check"></i> فاتورة بيع جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="@Url.Action("Create", "BOM")" class="btn btn-outline-warning w-100">
                            <i class="bi bi-diagram-3"></i> قائمة مواد جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
