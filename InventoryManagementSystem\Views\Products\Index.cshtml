@model IEnumerable<InventoryManagementSystem.Models.Product>

@{
    ViewData["Title"] = "المنتجات";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-box"></i> المنتجات</h1>
    <a asp-action="Create" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> إضافة منتج جديد
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الكود</th>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>الوحدة</th>
                            <th>المخزون الحالي</th>
                            <th>الحد الأدنى</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            var currentStock = item.Inventory?.CurrentStock ?? 0;
                            var isLowStock = currentStock <= item.MinimumStock;
                            
                            <tr class="@(isLowStock ? "table-warning" : "")">
                                <td>@Html.DisplayFor(modelItem => item.Code)</td>
                                <td>@Html.DisplayFor(modelItem => item.Name)</td>
                                <td>@Html.DisplayFor(modelItem => item.Category.Name)</td>
                                <td>@Html.DisplayFor(modelItem => item.Unit)</td>
                                <td>
                                    <span class="badge @(isLowStock ? "bg-warning" : "bg-success")">
                                        @currentStock
                                    </span>
                                </td>
                                <td>@Html.DisplayFor(modelItem => item.MinimumStock)</td>
                                <td>
                                    @if (isLowStock)
                                    {
                                        <span class="badge bg-warning">
                                            <i class="bi bi-exclamation-triangle"></i> مخزون قليل
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> متوفر
                                        </span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-outline-info btn-sm">
                                            <i class="bi bi-eye"></i> عرض
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-outline-warning btn-sm">
                                            <i class="bi bi-pencil"></i> تعديل
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i> حذف
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-box fs-1 text-muted"></i>
                <h4 class="text-muted mt-3">لا توجد منتجات</h4>
                <p class="text-muted">ابدأ بإضافة منتج جديد لإدارة مخزونك</p>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> إضافة منتج جديد
                </a>
            </div>
        }
    </div>
</div>
