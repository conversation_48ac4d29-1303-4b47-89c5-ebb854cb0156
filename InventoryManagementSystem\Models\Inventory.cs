using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagementSystem.Models
{
    public class Inventory
    {
        public int Id { get; set; }
        
        public int ProductId { get; set; }
        
        public int CurrentStock { get; set; } = 0; // الكمية الحالية
        
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        
        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
    
    public class InventoryTransaction
    {
        public int Id { get; set; }
        
        public int ProductId { get; set; }
        
        public TransactionType Type { get; set; }
        
        public int Quantity { get; set; }
        
        public int? PurchaseInvoiceId { get; set; }
        
        public int? SaleInvoiceId { get; set; }
        
        [StringLength(200)]
        public string? Reference { get; set; } // مرجع العملية
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        public DateTime TransactionDate { get; set; } = DateTime.Now;
        
        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
        
        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice? PurchaseInvoice { get; set; }
        
        [ForeignKey("SaleInvoiceId")]
        public virtual SaleInvoice? SaleInvoice { get; set; }
    }
    
    public enum TransactionType
    {
        Purchase = 1,    // شراء (زيادة)
        Sale = 2,        // بيع (نقص)
        Adjustment = 3,  // تعديل
        Production = 4,  // إنتاج (زيادة)
        Consumption = 5  // استهلاك في الإنتاج (نقص)
    }
}
