/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-mhn93aueuf] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-mhn93aueuf] {
  color: #0077cc;
}

.btn-primary[b-mhn93aueuf] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-mhn93aueuf], .nav-pills .show > .nav-link[b-mhn93aueuf] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-mhn93aueuf] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-mhn93aueuf] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-mhn93aueuf] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-mhn93aueuf] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-mhn93aueuf] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
