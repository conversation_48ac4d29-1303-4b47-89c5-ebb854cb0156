using InventoryManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace InventoryManagementSystem.Data
{
    public static class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            using var context = new ApplicationDbContext(
                serviceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>());

            // Check if data already exists
            if (context.Categories.Any())
            {
                return; // DB has been seeded
            }

            // Add Categories
            var categories = new[]
            {
                new Category { Name = "مواد خام", Description = "المواد الخام الأساسية" },
                new Category { Name = "منتجات نهائية", Description = "المنتجات الجاهزة للبيع" },
                new Category { Name = "قطع غيار", Description = "قطع الغيار والمكونات" },
                new Category { Name = "أدوات", Description = "الأدوات والمعدات" }
            };

            context.Categories.AddRange(categories);
            await context.SaveChangesAsync();

            // Add Suppliers
            var suppliers = new[]
            {
                new Supplier 
                { 
                    Name = "شركة المواد الخام المحدودة", 
                    Phone = "01234567890", 
                    Email = "<EMAIL>", 
                    Address = "الرياض، المملكة العربية السعودية" 
                },
                new Supplier 
                { 
                    Name = "مؤسسة التوريدات الصناعية", 
                    Phone = "01987654321", 
                    Email = "<EMAIL>", 
                    Address = "جدة، المملكة العربية السعودية" 
                }
            };

            context.Suppliers.AddRange(suppliers);
            await context.SaveChangesAsync();

            // Add Customers
            var customers = new[]
            {
                new Customer 
                { 
                    Name = "شركة التوزيع الكبرى", 
                    Phone = "01122334455", 
                    Email = "<EMAIL>", 
                    Address = "الدمام، المملكة العربية السعودية" 
                },
                new Customer 
                { 
                    Name = "متجر التجزئة الحديث", 
                    Phone = "01555666777", 
                    Email = "<EMAIL>", 
                    Address = "مكة المكرمة، المملكة العربية السعودية" 
                }
            };

            context.Customers.AddRange(customers);
            await context.SaveChangesAsync();

            // Add Products
            var products = new[]
            {
                new Product 
                { 
                    Name = "حديد خام", 
                    Code = "RAW001", 
                    Description = "حديد خام عالي الجودة", 
                    Unit = "كيلو", 
                    CategoryId = categories[0].Id, 
                    MinimumStock = 100 
                },
                new Product 
                { 
                    Name = "ألومنيوم", 
                    Code = "RAW002", 
                    Description = "ألومنيوم نقي", 
                    Unit = "كيلو", 
                    CategoryId = categories[0].Id, 
                    MinimumStock = 50 
                },
                new Product 
                { 
                    Name = "منتج نهائي أ", 
                    Code = "FIN001", 
                    Description = "منتج نهائي جاهز للبيع", 
                    Unit = "قطعة", 
                    CategoryId = categories[1].Id, 
                    MinimumStock = 20 
                },
                new Product 
                { 
                    Name = "منتج نهائي ب", 
                    Code = "FIN002", 
                    Description = "منتج نهائي متقدم", 
                    Unit = "قطعة", 
                    CategoryId = categories[1].Id, 
                    MinimumStock = 15 
                }
            };

            context.Products.AddRange(products);
            await context.SaveChangesAsync();

            // Add Inventory records
            var inventories = new[]
            {
                new Inventory { ProductId = products[0].Id, CurrentStock = 150 },
                new Inventory { ProductId = products[1].Id, CurrentStock = 75 },
                new Inventory { ProductId = products[2].Id, CurrentStock = 10 }, // Low stock
                new Inventory { ProductId = products[3].Id, CurrentStock = 25 }
            };

            context.Inventories.AddRange(inventories);
            await context.SaveChangesAsync();

            // Add a sample BOM
            var bom = new BOM
            {
                Name = "تصنيع المنتج النهائي أ",
                ProductId = products[2].Id, // منتج نهائي أ
                OutputQuantity = 1,
                Description = "قائمة المواد لتصنيع المنتج النهائي أ"
            };

            context.BOMs.Add(bom);
            await context.SaveChangesAsync();

            // Add BOM Items
            var bomItems = new[]
            {
                new BOMItem { BOMId = bom.Id, ProductId = products[0].Id, RequiredQuantity = 5 }, // 5 كيلو حديد
                new BOMItem { BOMId = bom.Id, ProductId = products[1].Id, RequiredQuantity = 2 }  // 2 كيلو ألومنيوم
            };

            context.BOMItems.AddRange(bomItems);
            await context.SaveChangesAsync();

            // Add sample Purchase Invoice
            var purchaseInvoice = new PurchaseInvoice
            {
                InvoiceNumber = "PUR-20250529-0001",
                InvoiceDate = DateTime.Now.AddDays(-5),
                SupplierId = suppliers[0].Id,
                Notes = "فاتورة شراء تجريبية"
            };

            context.PurchaseInvoices.Add(purchaseInvoice);
            await context.SaveChangesAsync();

            // Add Purchase Invoice Items
            var purchaseItems = new[]
            {
                new PurchaseInvoiceItem { PurchaseInvoiceId = purchaseInvoice.Id, ProductId = products[0].Id, Quantity = 100 },
                new PurchaseInvoiceItem { PurchaseInvoiceId = purchaseInvoice.Id, ProductId = products[1].Id, Quantity = 50 }
            };

            context.PurchaseInvoiceItems.AddRange(purchaseItems);
            await context.SaveChangesAsync();

            // Add Inventory Transactions for the purchase
            var transactions = new[]
            {
                new InventoryTransaction 
                { 
                    ProductId = products[0].Id, 
                    Type = TransactionType.Purchase, 
                    Quantity = 100, 
                    PurchaseInvoiceId = purchaseInvoice.Id,
                    Reference = $"Purchase Invoice #{purchaseInvoice.InvoiceNumber}",
                    TransactionDate = purchaseInvoice.InvoiceDate
                },
                new InventoryTransaction 
                { 
                    ProductId = products[1].Id, 
                    Type = TransactionType.Purchase, 
                    Quantity = 50, 
                    PurchaseInvoiceId = purchaseInvoice.Id,
                    Reference = $"Purchase Invoice #{purchaseInvoice.InvoiceNumber}",
                    TransactionDate = purchaseInvoice.InvoiceDate
                }
            };

            context.InventoryTransactions.AddRange(transactions);
            await context.SaveChangesAsync();
        }
    }
}
