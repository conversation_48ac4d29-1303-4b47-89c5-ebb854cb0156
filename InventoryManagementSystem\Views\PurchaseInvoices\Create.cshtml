@model InventoryManagementSystem.Models.PurchaseInvoice

@{
    ViewData["Title"] = "فاتورة شراء جديدة";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-plus-circle"></i> فاتورة شراء جديدة</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create" id="purchaseForm">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="SupplierId" class="form-label">المورد</label>
                                <select asp-for="SupplierId" class="form-select" asp-items="ViewBag.SupplierId">
                                    <option value="">اختر المورد</option>
                                </select>
                                <span asp-validation-for="SupplierId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="InvoiceDate" class="form-label">تاريخ الفاتورة</label>
                                <input asp-for="InvoiceDate" class="form-control" type="date" />
                                <span asp-validation-for="InvoiceDate" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Notes" class="form-label">الملاحظات</label>
                        <textarea asp-for="Notes" class="form-control" rows="2" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                        <span asp-validation-for="Notes" class="text-danger"></span>
                    </div>
                    
                    <hr>
                    
                    <h5><i class="bi bi-list"></i> أصناف الفاتورة</h5>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered" id="itemsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="itemsTableBody">
                                <tr>
                                    <td>
                                        <select name="ProductIds" class="form-select product-select">
                                            <option value="">اختر المنتج</option>
                                            @foreach (var product in ViewBag.Products as SelectList)
                                            {
                                                <option value="@product.Value">@product.Text</option>
                                            }
                                        </select>
                                    </td>
                                    <td>
                                        <input name="Quantities" type="number" class="form-control quantity-input" min="1" value="1" />
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-danger btn-sm remove-row">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mb-3">
                        <button type="button" class="btn btn-success" id="addRowBtn">
                            <i class="bi bi-plus"></i> إضافة صنف
                        </button>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> حفظ الفاتورة
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Add new row
            $('#addRowBtn').click(function() {
                var newRow = `
                    <tr>
                        <td>
                            <select name="ProductIds" class="form-select product-select">
                                <option value="">اختر المنتج</option>
                                @foreach (var product in ViewBag.Products as SelectList)
                                {
                                    <option value="@product.Value">@product.Text</option>
                                }
                            </select>
                        </td>
                        <td>
                            <input name="Quantities" type="number" class="form-control quantity-input" min="1" value="1" />
                        </td>
                        <td>
                            <button type="button" class="btn btn-danger btn-sm remove-row">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                $('#itemsTableBody').append(newRow);
            });
            
            // Remove row
            $(document).on('click', '.remove-row', function() {
                if ($('#itemsTableBody tr').length > 1) {
                    $(this).closest('tr').remove();
                } else {
                    alert('يجب أن تحتوي الفاتورة على صنف واحد على الأقل');
                }
            });
            
            // Form validation
            $('#purchaseForm').submit(function(e) {
                var hasValidItems = false;
                $('#itemsTableBody tr').each(function() {
                    var productId = $(this).find('.product-select').val();
                    var quantity = $(this).find('.quantity-input').val();
                    if (productId && quantity > 0) {
                        hasValidItems = true;
                        return false;
                    }
                });
                
                if (!hasValidItems) {
                    e.preventDefault();
                    alert('يجب إضافة صنف واحد على الأقل مع كمية صحيحة');
                }
            });
        });
    </script>
}
