using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using InventoryManagementSystem.Data;
using InventoryManagementSystem.Models;

namespace InventoryManagementSystem.Controllers
{
    public class BOMController : Controller
    {
        private readonly ApplicationDbContext _context;

        public BOMController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: BOM
        public async Task<IActionResult> Index()
        {
            var boms = await _context.BOMs
                .Include(b => b.Product)
                .Include(b => b.Items)
                .ThenInclude(i => i.Product)
                .ToListAsync();
            return View(boms);
        }

        // GET: BOM/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var bom = await _context.BOMs
                .Include(b => b.Product)
                .Include(b => b.Items)
                .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (bom == null)
            {
                return NotFound();
            }

            return View(bom);
        }

        // GET: BOM/Create
        public IActionResult Create()
        {
            ViewData["ProductId"] = new SelectList(_context.Products, "Id", "Name");
            ViewData["Materials"] = new SelectList(_context.Products, "Id", "Name");
            return View();
        }

        // POST: BOM/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(BOM bom, List<int> MaterialIds, List<int> RequiredQuantities)
        {
            if (ModelState.IsValid && MaterialIds.Count > 0)
            {
                _context.Add(bom);
                await _context.SaveChangesAsync();

                // Add BOM items
                for (int i = 0; i < MaterialIds.Count; i++)
                {
                    if (RequiredQuantities[i] > 0)
                    {
                        var bomItem = new BOMItem
                        {
                            BOMId = bom.Id,
                            ProductId = MaterialIds[i],
                            RequiredQuantity = RequiredQuantities[i]
                        };
                        _context.BOMItems.Add(bomItem);
                    }
                }

                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }

            ViewData["ProductId"] = new SelectList(_context.Products, "Id", "Name", bom.ProductId);
            ViewData["Materials"] = new SelectList(_context.Products, "Id", "Name");
            return View(bom);
        }

        // GET: BOM/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var bom = await _context.BOMs
                .Include(b => b.Items)
                .FirstOrDefaultAsync(b => b.Id == id);
            
            if (bom == null)
            {
                return NotFound();
            }

            ViewData["ProductId"] = new SelectList(_context.Products, "Id", "Name", bom.ProductId);
            ViewData["Materials"] = new SelectList(_context.Products, "Id", "Name");
            return View(bom);
        }

        // POST: BOM/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, BOM bom, List<int> MaterialIds, List<int> RequiredQuantities)
        {
            if (id != bom.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(bom);

                    // Remove existing BOM items
                    var existingItems = await _context.BOMItems.Where(bi => bi.BOMId == id).ToListAsync();
                    _context.BOMItems.RemoveRange(existingItems);

                    // Add new BOM items
                    for (int i = 0; i < MaterialIds.Count; i++)
                    {
                        if (RequiredQuantities[i] > 0)
                        {
                            var bomItem = new BOMItem
                            {
                                BOMId = bom.Id,
                                ProductId = MaterialIds[i],
                                RequiredQuantity = RequiredQuantities[i]
                            };
                            _context.BOMItems.Add(bomItem);
                        }
                    }

                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!BOMExists(bom.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewData["ProductId"] = new SelectList(_context.Products, "Id", "Name", bom.ProductId);
            ViewData["Materials"] = new SelectList(_context.Products, "Id", "Name");
            return View(bom);
        }

        // GET: BOM/Produce/5
        public async Task<IActionResult> Produce(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var bom = await _context.BOMs
                .Include(b => b.Product)
                .Include(b => b.Items)
                .ThenInclude(i => i.Product)
                .ThenInclude(p => p.Inventory)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (bom == null)
            {
                return NotFound();
            }

            // Check if materials are available
            var canProduce = true;
            var materialStatus = new List<dynamic>();

            foreach (var item in bom.Items)
            {
                var currentStock = item.Product.Inventory?.CurrentStock ?? 0;
                var isAvailable = currentStock >= item.RequiredQuantity;
                if (!isAvailable) canProduce = false;

                materialStatus.Add(new
                {
                    ProductName = item.Product.Name,
                    Required = item.RequiredQuantity,
                    Available = currentStock,
                    IsAvailable = isAvailable
                });
            }

            ViewBag.CanProduce = canProduce;
            ViewBag.MaterialStatus = materialStatus;

            return View(bom);
        }

        // POST: BOM/Produce/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ProduceConfirmed(int id, int quantity = 1)
        {
            var bom = await _context.BOMs
                .Include(b => b.Product)
                .Include(b => b.Items)
                .ThenInclude(i => i.Product)
                .ThenInclude(p => p.Inventory)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (bom == null)
            {
                return NotFound();
            }

            // Check materials availability
            foreach (var item in bom.Items)
            {
                var currentStock = item.Product.Inventory?.CurrentStock ?? 0;
                var requiredQuantity = item.RequiredQuantity * quantity;
                
                if (currentStock < requiredQuantity)
                {
                    TempData["Error"] = $"المواد الخام غير كافية للإنتاج. المطلوب: {requiredQuantity} من {item.Product.Name}، المتوفر: {currentStock}";
                    return RedirectToAction(nameof(Produce), new { id });
                }
            }

            // Consume materials
            foreach (var item in bom.Items)
            {
                await UpdateInventory(item.ProductId, item.RequiredQuantity * quantity, TransactionType.Consumption);
            }

            // Add produced items to inventory
            await UpdateInventory(bom.ProductId, bom.OutputQuantity * quantity, TransactionType.Production);

            await _context.SaveChangesAsync();

            TempData["Success"] = $"تم إنتاج {bom.OutputQuantity * quantity} {bom.Product.Unit} من {bom.Product.Name} بنجاح";
            return RedirectToAction(nameof(Index));
        }

        private async Task UpdateInventory(int productId, int quantity, TransactionType type)
        {
            var inventory = await _context.Inventories.FirstOrDefaultAsync(i => i.ProductId == productId);
            if (inventory == null)
            {
                inventory = new Inventory { ProductId = productId, CurrentStock = 0 };
                _context.Inventories.Add(inventory);
            }

            switch (type)
            {
                case TransactionType.Production:
                    inventory.CurrentStock += quantity;
                    break;
                case TransactionType.Consumption:
                    inventory.CurrentStock -= quantity;
                    break;
            }

            inventory.LastUpdated = DateTime.Now;

            var transaction = new InventoryTransaction
            {
                ProductId = productId,
                Type = type,
                Quantity = quantity,
                Reference = type == TransactionType.Production ? "Production" : "Material Consumption"
            };

            _context.InventoryTransactions.Add(transaction);
        }

        // GET: BOM/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var bom = await _context.BOMs
                .Include(b => b.Product)
                .Include(b => b.Items)
                .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (bom == null)
            {
                return NotFound();
            }

            return View(bom);
        }

        // POST: BOM/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var bom = await _context.BOMs.FindAsync(id);
            if (bom != null)
            {
                _context.BOMs.Remove(bom);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool BOMExists(int id)
        {
            return _context.BOMs.Any(e => e.Id == id);
        }
    }
}
