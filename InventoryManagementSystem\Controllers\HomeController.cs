using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using InventoryManagementSystem.Models;
using InventoryManagementSystem.Data;

namespace InventoryManagementSystem.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index()
    {
        // Dashboard data
        var dashboardData = new
        {
            TotalProducts = await _context.Products.CountAsync(),
            TotalCategories = await _context.Categories.CountAsync(),
            TotalSuppliers = await _context.Suppliers.CountAsync(),
            TotalCustomers = await _context.Customers.CountAsync(),
            LowStockProducts = await _context.Inventories
                .Include(i => i.Product)
                .Where(i => i.CurrentStock <= i.Product.MinimumStock)
                .CountAsync(),
            RecentPurchases = await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .OrderByDescending(p => p.InvoiceDate)
                .Take(5)
                .ToListAsync(),
            RecentSales = await _context.SaleInvoices
                .Include(s => s.Customer)
                .OrderByDescending(s => s.InvoiceDate)
                .Take(5)
                .ToListAsync()
        };

        ViewBag.DashboardData = dashboardData;
        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
