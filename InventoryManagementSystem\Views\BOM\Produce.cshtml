@model InventoryManagementSystem.Models.BOM

@{
    ViewData["Title"] = "إنتاج - " + Model.Name;
    var canProduce = ViewBag.CanProduce;
    var materialStatus = ViewBag.MaterialStatus;
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-gear"></i> إنتاج - @Model.Name</h4>
            </div>
            <div class="card-body">
                
                <!-- Product Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>معلومات المنتج النهائي</h5>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>المنتج:</strong></td>
                                <td>@Model.Product.Name</td>
                            </tr>
                            <tr>
                                <td><strong>كمية الإنتاج:</strong></td>
                                <td>@Model.OutputQuantity @Model.Product.Unit</td>
                            </tr>
                            <tr>
                                <td><strong>الوصف:</strong></td>
                                <td>@Model.Description</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Materials Status -->
                <h5>حالة المواد الخام</h5>
                <div class="table-responsive mb-4">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>المادة الخام</th>
                                <th>الكمية المطلوبة</th>
                                <th>الكمية المتاحة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var material in materialStatus)
                            {
                                <tr class="@(material.IsAvailable ? "" : "table-danger")">
                                    <td>@material.ProductName</td>
                                    <td>@material.Required</td>
                                    <td>@material.Available</td>
                                    <td>
                                        @if (material.IsAvailable)
                                        {
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle"></i> متوفر
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle"></i> غير كافي
                                            </span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
                
                <!-- Production Form -->
                @if (canProduce)
                {
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> جميع المواد الخام متوفرة. يمكنك البدء في الإنتاج.
                    </div>
                    
                    <form asp-action="ProduceConfirmed" method="post">
                        <input type="hidden" name="id" value="@Model.Id" />
                        
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="quantity" class="form-label">كمية الإنتاج</label>
                                <input type="number" name="quantity" id="quantity" class="form-control" min="1" value="1" />
                                <small class="text-muted">سيتم إنتاج @Model.OutputQuantity @Model.Product.Unit لكل وحدة</small>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-success" onclick="return confirm('هل أنت متأكد من بدء عملية الإنتاج؟')">
                                <i class="bi bi-gear"></i> بدء الإنتاج
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> العودة للقائمة
                            </a>
                        </div>
                    </form>
                }
                else
                {
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> لا يمكن بدء الإنتاج. بعض المواد الخام غير متوفرة بالكمية المطلوبة.
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a asp-controller="PurchaseInvoices" asp-action="Create" class="btn btn-primary">
                            <i class="bi bi-cart-plus"></i> شراء المواد المطلوبة
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@if (TempData["Success"] != null)
{
    <script>
        $(document).ready(function() {
            alert('@TempData["Success"]');
        });
    </script>
}

@if (TempData["Error"] != null)
{
    <script>
        $(document).ready(function() {
            alert('@TempData["Error"]');
        });
    </script>
}
