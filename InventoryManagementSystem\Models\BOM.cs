using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryManagementSystem.Models
{
    // Bill of Materials - قائمة المواد
    public class BOM
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        public int ProductId { get; set; } // المنتج النهائي
        
        public int OutputQuantity { get; set; } = 1; // كمية الإنتاج المتوقعة
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
        
        public virtual ICollection<BOMItem> Items { get; set; } = new List<BOMItem>();
    }
    
    // عناصر قائمة المواد
    public class BOMItem
    {
        public int Id { get; set; }
        
        public int BOMId { get; set; }
        
        public int ProductId { get; set; } // المادة الخام المطلوبة
        
        public int RequiredQuantity { get; set; } // الكمية المطلوبة
        
        // Navigation Properties
        [ForeignKey("BOMId")]
        public virtual BOM BOM { get; set; } = null!;
        
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
