using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using InventoryManagementSystem.Data;
using InventoryManagementSystem.Models;

namespace InventoryManagementSystem.Controllers
{
    public class SaleInvoicesController : Controller
    {
        private readonly ApplicationDbContext _context;

        public SaleInvoicesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: SaleInvoices
        public async Task<IActionResult> Index()
        {
            var saleInvoices = await _context.SaleInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .ToListAsync();
            return View(saleInvoices);
        }

        // GET: SaleInvoices/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var saleInvoice = await _context.SaleInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (saleInvoice == null)
            {
                return NotFound();
            }

            return View(saleInvoice);
        }

        // GET: SaleInvoices/Create
        public async Task<IActionResult> Create()
        {
            ViewData["CustomerId"] = new SelectList(_context.Customers, "Id", "Name");
            
            // Get products with current stock
            var productsWithStock = await _context.Products
                .Include(p => p.Inventory)
                .Select(p => new {
                    p.Id,
                    DisplayName = $"{p.Name} (المخزون: {(p.Inventory != null ? p.Inventory.CurrentStock : 0)} {p.Unit})",
                    CurrentStock = p.Inventory != null ? p.Inventory.CurrentStock : 0
                })
                .ToListAsync();
            
            ViewData["Products"] = new SelectList(productsWithStock, "Id", "DisplayName");
            ViewBag.ProductsStock = productsWithStock.ToDictionary(p => p.Id, p => p.CurrentStock);
            
            return View();
        }

        // POST: SaleInvoices/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(SaleInvoice saleInvoice, List<int> ProductIds, List<int> Quantities)
        {
            if (ModelState.IsValid && ProductIds.Count > 0)
            {
                // Check stock availability
                for (int i = 0; i < ProductIds.Count; i++)
                {
                    if (Quantities[i] > 0)
                    {
                        var inventory = await _context.Inventories
                            .FirstOrDefaultAsync(inv => inv.ProductId == ProductIds[i]);
                        
                        if (inventory == null || inventory.CurrentStock < Quantities[i])
                        {
                            var product = await _context.Products.FindAsync(ProductIds[i]);
                            ModelState.AddModelError("", $"المخزون غير كافي للمنتج: {product?.Name}. المتوفر: {inventory?.CurrentStock ?? 0}");
                            
                            ViewData["CustomerId"] = new SelectList(_context.Customers, "Id", "Name", saleInvoice.CustomerId);
                            var productsWithStock = await _context.Products
                                .Include(p => p.Inventory)
                                .Select(p => new {
                                    p.Id,
                                    DisplayName = $"{p.Name} (المخزون: {(p.Inventory != null ? p.Inventory.CurrentStock : 0)} {p.Unit})",
                                    CurrentStock = p.Inventory != null ? p.Inventory.CurrentStock : 0
                                })
                                .ToListAsync();
                            
                            ViewData["Products"] = new SelectList(productsWithStock, "Id", "DisplayName");
                            ViewBag.ProductsStock = productsWithStock.ToDictionary(p => p.Id, p => p.CurrentStock);
                            
                            return View(saleInvoice);
                        }
                    }
                }

                // Generate invoice number
                var lastInvoice = await _context.SaleInvoices
                    .OrderByDescending(s => s.Id)
                    .FirstOrDefaultAsync();
                
                var invoiceNumber = $"SAL-{DateTime.Now:yyyyMMdd}-{(lastInvoice?.Id + 1 ?? 1):D4}";
                saleInvoice.InvoiceNumber = invoiceNumber;

                _context.Add(saleInvoice);
                await _context.SaveChangesAsync();

                // Add invoice items and update inventory
                for (int i = 0; i < ProductIds.Count; i++)
                {
                    if (Quantities[i] > 0)
                    {
                        var item = new SaleInvoiceItem
                        {
                            SaleInvoiceId = saleInvoice.Id,
                            ProductId = ProductIds[i],
                            Quantity = Quantities[i]
                        };
                        _context.SaleInvoiceItems.Add(item);

                        // Update inventory
                        await UpdateInventory(ProductIds[i], Quantities[i], TransactionType.Sale, saleInvoice.Id);
                    }
                }

                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }

            ViewData["CustomerId"] = new SelectList(_context.Customers, "Id", "Name", saleInvoice.CustomerId);
            var products = await _context.Products
                .Include(p => p.Inventory)
                .Select(p => new {
                    p.Id,
                    DisplayName = $"{p.Name} (المخزون: {(p.Inventory != null ? p.Inventory.CurrentStock : 0)} {p.Unit})",
                    CurrentStock = p.Inventory != null ? p.Inventory.CurrentStock : 0
                })
                .ToListAsync();
            
            ViewData["Products"] = new SelectList(products, "Id", "DisplayName");
            ViewBag.ProductsStock = products.ToDictionary(p => p.Id, p => p.CurrentStock);
            
            return View(saleInvoice);
        }

        private async Task UpdateInventory(int productId, int quantity, TransactionType type, int? invoiceId = null)
        {
            var inventory = await _context.Inventories.FirstOrDefaultAsync(i => i.ProductId == productId);
            if (inventory == null)
            {
                inventory = new Inventory { ProductId = productId, CurrentStock = 0 };
                _context.Inventories.Add(inventory);
            }

            // Update stock based on transaction type
            switch (type)
            {
                case TransactionType.Purchase:
                case TransactionType.Production:
                    inventory.CurrentStock += quantity;
                    break;
                case TransactionType.Sale:
                case TransactionType.Consumption:
                    inventory.CurrentStock -= quantity;
                    break;
                case TransactionType.Adjustment:
                    inventory.CurrentStock = quantity;
                    break;
            }

            inventory.LastUpdated = DateTime.Now;

            // Create transaction record
            var transaction = new InventoryTransaction
            {
                ProductId = productId,
                Type = type,
                Quantity = quantity,
                PurchaseInvoiceId = type == TransactionType.Purchase ? invoiceId : null,
                SaleInvoiceId = type == TransactionType.Sale ? invoiceId : null,
                Reference = type == TransactionType.Purchase ? $"Purchase Invoice #{invoiceId}" : 
                           type == TransactionType.Sale ? $"Sale Invoice #{invoiceId}" : "Manual Adjustment"
            };

            _context.InventoryTransactions.Add(transaction);
        }

        // GET: SaleInvoices/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var saleInvoice = await _context.SaleInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (saleInvoice == null)
            {
                return NotFound();
            }

            return View(saleInvoice);
        }

        // POST: SaleInvoices/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var saleInvoice = await _context.SaleInvoices
                .Include(s => s.Items)
                .FirstOrDefaultAsync(s => s.Id == id);
            
            if (saleInvoice != null)
            {
                // Reverse inventory changes
                foreach (var item in saleInvoice.Items)
                {
                    var inventory = await _context.Inventories.FirstOrDefaultAsync(i => i.ProductId == item.ProductId);
                    if (inventory != null)
                    {
                        inventory.CurrentStock += item.Quantity; // Add back the sold quantity
                        inventory.LastUpdated = DateTime.Now;
                    }
                }

                _context.SaleInvoices.Remove(saleInvoice);
                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));
        }
    }
}
