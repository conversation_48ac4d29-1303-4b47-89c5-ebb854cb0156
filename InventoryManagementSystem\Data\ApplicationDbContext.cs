using Microsoft.EntityFrameworkCore;
using InventoryManagementSystem.Models;

namespace InventoryManagementSystem.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Category> Categories { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; }
        public DbSet<SaleInvoice> SaleInvoices { get; set; }
        public DbSet<SaleInvoiceItem> SaleInvoiceItems { get; set; }
        public DbSet<BOM> BOMs { get; set; }
        public DbSet<BOMItem> BOMItems { get; set; }
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships and constraints
            
            // Product - Category relationship
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Product - Inventory relationship (One-to-One)
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Inventory)
                .WithOne(i => i.Product)
                .HasForeignKey<Inventory>(i => i.ProductId);

            // Purchase Invoice relationships
            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.Supplier)
                .WithMany(s => s.PurchaseInvoices)
                .HasForeignKey(pi => pi.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PurchaseInvoiceItem>()
                .HasOne(pii => pii.PurchaseInvoice)
                .WithMany(pi => pi.Items)
                .HasForeignKey(pii => pii.PurchaseInvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<PurchaseInvoiceItem>()
                .HasOne(pii => pii.Product)
                .WithMany(p => p.PurchaseInvoiceItems)
                .HasForeignKey(pii => pii.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // Sale Invoice relationships
            modelBuilder.Entity<SaleInvoice>()
                .HasOne(si => si.Customer)
                .WithMany(c => c.SaleInvoices)
                .HasForeignKey(si => si.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SaleInvoiceItem>()
                .HasOne(sii => sii.SaleInvoice)
                .WithMany(si => si.Items)
                .HasForeignKey(sii => sii.SaleInvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<SaleInvoiceItem>()
                .HasOne(sii => sii.Product)
                .WithMany(p => p.SaleInvoiceItems)
                .HasForeignKey(sii => sii.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // BOM relationships
            modelBuilder.Entity<BOM>()
                .HasOne(b => b.Product)
                .WithMany(p => p.BOMs)
                .HasForeignKey(b => b.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<BOMItem>()
                .HasOne(bi => bi.BOM)
                .WithMany(b => b.Items)
                .HasForeignKey(bi => bi.BOMId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<BOMItem>()
                .HasOne(bi => bi.Product)
                .WithMany(p => p.BOMItems)
                .HasForeignKey(bi => bi.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            // Inventory Transaction relationships
            modelBuilder.Entity<InventoryTransaction>()
                .HasOne(it => it.Product)
                .WithMany(p => p.InventoryTransactions)
                .HasForeignKey(it => it.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InventoryTransaction>()
                .HasOne(it => it.PurchaseInvoice)
                .WithMany()
                .HasForeignKey(it => it.PurchaseInvoiceId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<InventoryTransaction>()
                .HasOne(it => it.SaleInvoice)
                .WithMany()
                .HasForeignKey(it => it.SaleInvoiceId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes for better performance
            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Code)
                .IsUnique();

            modelBuilder.Entity<PurchaseInvoice>()
                .HasIndex(pi => pi.InvoiceNumber)
                .IsUnique();

            modelBuilder.Entity<SaleInvoice>()
                .HasIndex(si => si.InvoiceNumber)
                .IsUnique();
        }
    }
}
