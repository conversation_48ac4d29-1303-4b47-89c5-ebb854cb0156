using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using InventoryManagementSystem.Data;
using InventoryManagementSystem.Models;

namespace InventoryManagementSystem.Controllers
{
    public class PurchaseInvoicesController : Controller
    {
        private readonly ApplicationDbContext _context;

        public PurchaseInvoicesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: PurchaseInvoices
        public async Task<IActionResult> Index()
        {
            var purchaseInvoices = await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .ToListAsync();
            return View(purchaseInvoices);
        }

        // GET: PurchaseInvoices/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseInvoice = await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (purchaseInvoice == null)
            {
                return NotFound();
            }

            return View(purchaseInvoice);
        }

        // GET: PurchaseInvoices/Create
        public IActionResult Create()
        {
            ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name");
            ViewData["Products"] = new SelectList(_context.Products, "Id", "Name");
            return View();
        }

        // POST: PurchaseInvoices/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PurchaseInvoice purchaseInvoice, List<int> ProductIds, List<int> Quantities)
        {
            if (ModelState.IsValid && ProductIds.Count > 0)
            {
                // Generate invoice number
                var lastInvoice = await _context.PurchaseInvoices
                    .OrderByDescending(p => p.Id)
                    .FirstOrDefaultAsync();
                
                var invoiceNumber = $"PUR-{DateTime.Now:yyyyMMdd}-{(lastInvoice?.Id + 1 ?? 1):D4}";
                purchaseInvoice.InvoiceNumber = invoiceNumber;

                _context.Add(purchaseInvoice);
                await _context.SaveChangesAsync();

                // Add invoice items and update inventory
                for (int i = 0; i < ProductIds.Count; i++)
                {
                    if (Quantities[i] > 0)
                    {
                        var item = new PurchaseInvoiceItem
                        {
                            PurchaseInvoiceId = purchaseInvoice.Id,
                            ProductId = ProductIds[i],
                            Quantity = Quantities[i]
                        };
                        _context.PurchaseInvoiceItems.Add(item);

                        // Update inventory
                        await UpdateInventory(ProductIds[i], Quantities[i], TransactionType.Purchase, purchaseInvoice.Id);
                    }
                }

                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }

            ViewData["SupplierId"] = new SelectList(_context.Suppliers, "Id", "Name", purchaseInvoice.SupplierId);
            ViewData["Products"] = new SelectList(_context.Products, "Id", "Name");
            return View(purchaseInvoice);
        }

        private async Task UpdateInventory(int productId, int quantity, TransactionType type, int? invoiceId = null)
        {
            var inventory = await _context.Inventories.FirstOrDefaultAsync(i => i.ProductId == productId);
            if (inventory == null)
            {
                inventory = new Inventory { ProductId = productId, CurrentStock = 0 };
                _context.Inventories.Add(inventory);
            }

            // Update stock based on transaction type
            switch (type)
            {
                case TransactionType.Purchase:
                case TransactionType.Production:
                    inventory.CurrentStock += quantity;
                    break;
                case TransactionType.Sale:
                case TransactionType.Consumption:
                    inventory.CurrentStock -= quantity;
                    break;
                case TransactionType.Adjustment:
                    inventory.CurrentStock = quantity; // Set to exact quantity for adjustments
                    break;
            }

            inventory.LastUpdated = DateTime.Now;

            // Create transaction record
            var transaction = new InventoryTransaction
            {
                ProductId = productId,
                Type = type,
                Quantity = quantity,
                PurchaseInvoiceId = type == TransactionType.Purchase ? invoiceId : null,
                SaleInvoiceId = type == TransactionType.Sale ? invoiceId : null,
                Reference = type == TransactionType.Purchase ? $"Purchase Invoice #{invoiceId}" : 
                           type == TransactionType.Sale ? $"Sale Invoice #{invoiceId}" : "Manual Adjustment"
            };

            _context.InventoryTransactions.Add(transaction);
        }

        // GET: PurchaseInvoices/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseInvoice = await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (purchaseInvoice == null)
            {
                return NotFound();
            }

            return View(purchaseInvoice);
        }

        // POST: PurchaseInvoices/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var purchaseInvoice = await _context.PurchaseInvoices
                .Include(p => p.Items)
                .FirstOrDefaultAsync(p => p.Id == id);
            
            if (purchaseInvoice != null)
            {
                // Reverse inventory changes
                foreach (var item in purchaseInvoice.Items)
                {
                    await UpdateInventory(item.ProductId, -item.Quantity, TransactionType.Adjustment);
                }

                _context.PurchaseInvoices.Remove(purchaseInvoice);
                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));
        }
    }
}
