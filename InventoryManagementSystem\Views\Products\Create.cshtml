@model InventoryManagementSystem.Models.Product

@{
    ViewData["Title"] = "إضافة منتج جديد";
}

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-plus-circle"></i> إضافة منتج جديد</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Name" class="form-label">اسم المنتج</label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم المنتج" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Code" class="form-label">كود المنتج</label>
                                <input asp-for="Code" class="form-control" placeholder="أدخل كود المنتج (اختياري)" />
                                <span asp-validation-for="Code" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Description" class="form-label">الوصف</label>
                        <textarea asp-for="Description" class="form-control" rows="3" placeholder="أدخل وصف المنتج (اختياري)"></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="CategoryId" class="form-label">الفئة</label>
                                <select asp-for="CategoryId" class="form-select" asp-items="ViewBag.CategoryId">
                                    <option value="">اختر الفئة</option>
                                </select>
                                <span asp-validation-for="CategoryId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="Unit" class="form-label">الوحدة</label>
                                <select asp-for="Unit" class="form-select">
                                    <option value="قطعة">قطعة</option>
                                    <option value="كيلو">كيلو</option>
                                    <option value="متر">متر</option>
                                    <option value="لتر">لتر</option>
                                    <option value="علبة">علبة</option>
                                    <option value="كرتونة">كرتونة</option>
                                    <option value="طن">طن</option>
                                    <option value="جرام">جرام</option>
                                </select>
                                <span asp-validation-for="Unit" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label asp-for="MinimumStock" class="form-label">الحد الأدنى للمخزون</label>
                                <input asp-for="MinimumStock" class="form-control" type="number" min="0" value="0" />
                                <span asp-validation-for="MinimumStock" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> حفظ
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
