@model IEnumerable<InventoryManagementSystem.Models.Category>

@{
    ViewData["Title"] = "الفئات";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-tags"></i> الفئات</h1>
    <a asp-action="Create" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> إضافة فئة جديدة
    </a>
</div>

<div class="card">
    <div class="card-body">
        @if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الاسم</th>
                            <th>الوصف</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@Html.DisplayFor(modelItem => item.Name)</td>
                                <td>@Html.DisplayFor(modelItem => item.Description)</td>
                                <td>@item.CreatedAt.ToString("yyyy/MM/dd")</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-outline-info btn-sm">
                                            <i class="bi bi-eye"></i> عرض
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-outline-warning btn-sm">
                                            <i class="bi bi-pencil"></i> تعديل
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i> حذف
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-tags fs-1 text-muted"></i>
                <h4 class="text-muted mt-3">لا توجد فئات</h4>
                <p class="text-muted">ابدأ بإضافة فئة جديدة لتنظيم منتجاتك</p>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> إضافة فئة جديدة
                </a>
            </div>
        }
    </div>
</div>
