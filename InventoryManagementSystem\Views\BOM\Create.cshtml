@model InventoryManagementSystem.Models.BOM

@{
    ViewData["Title"] = "إضافة قائمة مواد جديدة";
}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-plus-circle"></i> إضافة قائمة مواد جديدة</h4>
            </div>
            <div class="card-body">
                <form asp-action="Create" id="bomForm">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label asp-for="Name" class="form-label">اسم قائمة المواد</label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم قائمة المواد" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label asp-for="ProductId" class="form-label">المنتج النهائي</label>
                                <select asp-for="ProductId" class="form-select" asp-items="ViewBag.ProductId">
                                    <option value="">اختر المنتج</option>
                                </select>
                                <span asp-validation-for="ProductId" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label asp-for="OutputQuantity" class="form-label">كمية الإنتاج</label>
                                <input asp-for="OutputQuantity" class="form-control" type="number" min="1" value="1" />
                                <span asp-validation-for="OutputQuantity" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Description" class="form-label">الوصف</label>
                        <textarea asp-for="Description" class="form-control" rows="2" placeholder="وصف قائمة المواد (اختياري)"></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>
                    
                    <hr>
                    
                    <h5><i class="bi bi-list"></i> المواد المطلوبة</h5>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered" id="materialsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>المادة الخام</th>
                                    <th>الكمية المطلوبة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="materialsTableBody">
                                <tr>
                                    <td>
                                        <select name="MaterialIds" class="form-select material-select">
                                            <option value="">اختر المادة الخام</option>
                                            @foreach (var material in ViewBag.Materials as SelectList)
                                            {
                                                <option value="@material.Value">@material.Text</option>
                                            }
                                        </select>
                                    </td>
                                    <td>
                                        <input name="RequiredQuantities" type="number" class="form-control quantity-input" min="1" value="1" />
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-danger btn-sm remove-row">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mb-3">
                        <button type="button" class="btn btn-success" id="addMaterialBtn">
                            <i class="bi bi-plus"></i> إضافة مادة خام
                        </button>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> حفظ قائمة المواد
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Add new material row
            $('#addMaterialBtn').click(function() {
                var newRow = `
                    <tr>
                        <td>
                            <select name="MaterialIds" class="form-select material-select">
                                <option value="">اختر المادة الخام</option>
                                @foreach (var material in ViewBag.Materials as SelectList)
                                {
                                    <option value="@material.Value">@material.Text</option>
                                }
                            </select>
                        </td>
                        <td>
                            <input name="RequiredQuantities" type="number" class="form-control quantity-input" min="1" value="1" />
                        </td>
                        <td>
                            <button type="button" class="btn btn-danger btn-sm remove-row">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                $('#materialsTableBody').append(newRow);
            });
            
            // Remove row
            $(document).on('click', '.remove-row', function() {
                if ($('#materialsTableBody tr').length > 1) {
                    $(this).closest('tr').remove();
                } else {
                    alert('يجب أن تحتوي قائمة المواد على مادة خام واحدة على الأقل');
                }
            });
            
            // Prevent selecting the same material twice
            $(document).on('change', '.material-select', function() {
                var selectedMaterials = [];
                $('.material-select').each(function() {
                    var value = $(this).val();
                    if (value) {
                        selectedMaterials.push(value);
                    }
                });
                
                // Check for duplicates
                var duplicates = selectedMaterials.filter((item, index) => selectedMaterials.indexOf(item) !== index);
                if (duplicates.length > 0) {
                    alert('لا يمكن اختيار نفس المادة الخام أكثر من مرة');
                    $(this).val('');
                }
            });
            
            // Form validation
            $('#bomForm').submit(function(e) {
                var hasValidMaterials = false;
                $('#materialsTableBody tr').each(function() {
                    var materialId = $(this).find('.material-select').val();
                    var quantity = $(this).find('.quantity-input').val();
                    if (materialId && quantity > 0) {
                        hasValidMaterials = true;
                        return false;
                    }
                });
                
                if (!hasValidMaterials) {
                    e.preventDefault();
                    alert('يجب إضافة مادة خام واحدة على الأقل مع كمية صحيحة');
                }
            });
        });
    </script>
}
